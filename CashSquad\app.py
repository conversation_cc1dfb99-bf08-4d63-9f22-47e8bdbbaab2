"""Main application file for the finance tracker"""
from typing import Dict, Any, Optional

from data_entry import get_amount, get_category, get_date, get_description
from database import FinanceDatabase
from reports import (
    format_currency, 
    generate_summary, 
    plot_transactions_over_time,
    plot_monthly_summary
)
import config


class FinanceApp:
    """Main application class for the finance tracker"""
    
    def __init__(self):
        self.db = FinanceDatabase()
        self.running = False
    
    def add_transaction(self) -> None:
        """Add a new transaction"""
        print("\n=== Add New Transaction ===")
        date = get_date(
            "Enter the date (dd-mm-yyyy) or press Enter for today: ",
            allow_default=True,
        )
        amount = get_amount()
        category = get_category()
        description = get_description()
        
        if self.db.add_entry(date, amount, category, description):
            print("Transaction added successfully!")
        else:
            print("Failed to add transaction. Please try again.")
    
    def view_transactions(self) -> None:
        """View transactions within a date range"""
        print("\n=== View Transactions ===")
        start_date = get_date("Enter the start date (dd-mm-yyyy): ")
        end_date = get_date("Enter the end date (dd-mm-yyyy): ")
        
        df = self.db.get_transactions(start_date, end_date)
        
        if df.empty:
            print("No transactions found in the given date range.")
            return
            
        print(f"\nTransactions from {start_date} to {end_date}")
        print(df.to_string(
            index=False, 
            formatters={"date": lambda x: x.strftime(config.DATE_FORMAT)}
        ))
        
        summary = generate_summary(df)
        print("\nSummary:")
        print(f"Total Income: {format_currency(summary['income'])}")
        print(f"Total Expense: {format_currency(summary['expense'])}")
        print(f"Net Savings: {format_currency(summary['savings'])}")
        
        print("\nVisualization Options:")
        print("1. Time Series Plot")
        print("2. Monthly Summary")
        print("3. Return to Main Menu")
        
        viz_choice = input("Choose a visualization (1-3): ")
        
        if viz_choice == "1":
            plot_transactions_over_time(df, config.DATE_FORMAT)
        elif viz_choice == "2":
            plot_monthly_summary(df, config.DATE_FORMAT)
    
    def run(self) -> None:
        """Run the main application loop"""
        self.running = True
        
        print(f"\nWelcome to {config.APP_NAME} v{config.VERSION}")
        
        while self.running:
            print("\n=== Main Menu ===")
            print("1. Add a new transaction")
            print("2. View transactions and summary")
            print("3. Exit")
            
            choice = input("Enter your choice (1-3): ")
            
            if choice == "1":
                self.add_transaction()
            elif choice == "2":
                self.view_transactions()
            elif choice == "3":
                print(f"Thank you for using {config.APP_NAME}!")
                self.running = False
            else:
                print("Invalid choice. Please enter 1, 2, or 3.")


if __name__ == "__main__":
    app = FinanceApp()
    app.run()