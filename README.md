# CashSquad Finance Tracker

CashSquad is a comprehensive personal finance management application designed to replace traditional tools like Mint while offering advanced features for budget tracking, expense management, and financial analysis. Built with Python, it provides both command-line interface and data visualization capabilities to help users take control of their financial health.

## 🎯 Project Vision

CashSquad aims to be an all-in-one financial management software that empowers users with:

- **Comprehensive Tracking**: Monitor income, expenses, and savings patterns
- **Advanced Analytics**: Generate insights through trend analysis and visualizations
- **User-Friendly Interface**: Intuitive design suitable for users with varying financial literacy
- **Security-First Approach**: Robust data protection and privacy measures
- **Extensible Architecture**: Modular design for future enhancements

## ✨ Current Features

### Core Functionality

- **Transaction Management**: Add, view, and categorize financial transactions
- **Date Range Filtering**: View transactions within specific time periods
- **Financial Summaries**: Automatic calculation of income, expenses, and net savings
- **Data Visualization**: Interactive charts and graphs for financial trends
- **CSV Data Storage**: Reliable local data persistence

### Visualization & Reports

- **Time Series Analysis**: Track income and expenses over time
- **Monthly Summaries**: Bar charts showing monthly financial performance
- **Category Breakdown**: Analyze spending patterns by transaction type
- **Net Savings Tracking**: Monitor financial progress and goals

### User Experience

- **Input Validation**: Robust error handling and data validation
- **Flexible Date Entry**: Support for custom dates or automatic current date
- **Interactive Menus**: Clean command-line interface with clear navigation
- **Configurable Settings**: Customizable currency symbols, date formats, and display options

## 🏗️ Architecture

### Project Structure

```
CashSquad/
├── app.py              # Main application with FinanceApp class
├── main.py             # Alternative entry point with FinanceManager class
├── config.py           # Configuration settings and constants
├── data_entry.py       # Input validation and user interaction utilities
├── database.py         # Data persistence layer (currently CSV-based)
├── reports.py          # Visualization and reporting functionality
├── finance_data.csv    # Data storage file
└── SDD.md             # Software Design Document
```

### Core Components

#### FinanceApp Class (`app.py`)

- Main application controller
- Handles user interface and menu navigation
- Integrates all modules for seamless operation

#### FinanceManager Class (`main.py`)

- Alternative implementation with direct CSV operations
- Provides core transaction management functionality
- Includes built-in plotting capabilities

#### Data Entry Module (`data_entry.py`)

- Robust input validation system
- Date, amount, and category validation
- Error handling with user-friendly messages

#### Reports Module (`reports.py`)

- Financial summary generation
- Data visualization using matplotlib
- Configurable chart styling and formatting

#### Configuration (`config.py`)

- Centralized application settings
- Customizable display options
- Version and branding information

## 🚀 Getting Started

### Prerequisites

- Python 3.7 or higher
- Required packages:
  - pandas
  - matplotlib
  - datetime (built-in)
  - csv (built-in)

### Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd Python-Projects/CashSquad
   ```

2. Install dependencies:

   ```bash
   pip install pandas matplotlib
   ```

3. Run the application:

   ```bash
   python app.py
   # or alternatively
   python main.py
   ```

### Quick Start Guide

1. **Add a Transaction**: Choose option 1 from the main menu
   - Enter date (dd-mm-yyyy format) or press Enter for today
   - Input amount (positive numbers only)
   - Select category: 'I' for Income or 'E' for Expense
   - Add optional description

2. **View Financial Summary**: Choose option 2 from the main menu
   - Specify date range for analysis
   - Review transaction list and summary statistics
   - Generate visualizations for trend analysis

3. **Exit**: Choose option 3 to safely close the application

## 📊 Usage Examples

### Adding Transactions

```
Enter the date (dd-mm-yyyy) or press Enter for today: 15-12-2024
Enter the amount: 2500.00
Enter the category ('I' for Income or 'E' for Expense): I
Enter a description (optional): Salary payment
```

### Viewing Reports

```
Enter the start date (dd-mm-yyyy): 01-12-2024
Enter the end date (dd-mm-yyyy): 31-12-2024

Summary:
Total Income: $5000.00
Total Expense: $2300.00
Net Savings: $2700.00
```

## 🔧 Configuration

The application can be customized through `config.py`:

- **Currency Symbol**: Change `CURRENCY_SYMBOL` for different currencies
- **Date Format**: Modify `DATE_FORMAT` for regional preferences
- **Categories**: Extend `CATEGORIES` dictionary for additional transaction types
- **Visualization**: Adjust `PLOT_FIGSIZE` and color schemes
- **File Settings**: Configure CSV file location and naming

## 🛣️ Roadmap

Based on the Software Design Document, future enhancements include:

### Planned Features

- **Budget Tracker**: Set and monitor spending limits by category
- **Investment Tracking**: Portfolio management and performance analysis
- **Goal Setting**: Financial target setting and progress tracking
- **Multi-Currency Support**: Handle international transactions
- **Financial Institution Integration**: Automatic transaction import
- **Advanced Security**: Multi-factor authentication and encryption
- **Web Interface**: Browser-based access and mobile responsiveness
- **Customizable Reports**: User-defined report templates

### Technical Improvements

- **Database Migration**: Transition from CSV to robust database system
- **API Development**: RESTful API for external integrations
- **Cloud Sync**: Multi-device data synchronization
- **Performance Optimization**: Enhanced data processing for large datasets
- **Testing Framework**: Comprehensive unit and integration tests

## 🤝 Contributing

We welcome contributions to CashSquad! Areas where help is needed:

- **Feature Development**: Implement planned features from the roadmap
- **Testing**: Create comprehensive test suites
- **Documentation**: Improve user guides and technical documentation
- **UI/UX**: Enhance user interface design and experience
- **Security**: Implement advanced security measures
- **Performance**: Optimize data processing and visualization

## 📄 License

This project is part of the Python-Projects repository. Please refer to the repository's license for usage terms.

## 🆘 Support

For questions, bug reports, or feature requests:

1. Check the Software Design Document (`SDD.md`) for detailed project information
2. Review existing issues in the repository
3. Create a new issue with detailed description and steps to reproduce

## 📈 Version History

- **v1.0.0**: Initial release with core transaction management and visualization features

---

**CashSquad Finance Tracker** - Taking control of your financial future, one transaction at a time.
