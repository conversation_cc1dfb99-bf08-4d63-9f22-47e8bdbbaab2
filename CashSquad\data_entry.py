from datetime import datetime
from typing import Optional, Union, Dict, Any, Callable


date_format = "%d-%m-%Y"
CATEGORIES = {"I": "Income", "E": "Expense"}


def validate_input(
    prompt: str,
    validator: Callable[[str], Any],
    error_msg: str,
    allow_empty: bool = False,
    default_value: Any = None
) -> Any:
    """Generic input validator with retry logic"""
    user_input = input(prompt)
    
    if allow_empty and not user_input:
        return default_value
        
    try:
        return validator(user_input)
    except (ValueError, TypeError) as e:
        print(f"{error_msg}: {str(e)}")
        return validate_input(prompt, validator, error_msg, allow_empty, default_value)


def validate_date(date_str: str) -> str:
    """Validate and format a date string"""
    valid_date = datetime.strptime(date_str, date_format)
    return valid_date.strftime(date_format)


def validate_amount(amount_str: str) -> float:
    """Validate that amount is a positive number"""
    amount = float(amount_str)
    if amount <= 0:
        raise ValueError("Amount must be a positive non-zero value")
    return amount


def validate_category(category_str: str) -> str:
    """Validate that category is valid"""
    category = category_str.upper()
    if category not in CATEGORIES:
        raise ValueError("Must be 'I' for Income or 'E' for Expense")
    return CATEGORIES[category]


def get_date(prompt: str, allow_default: bool = False) -> str:
    """Get a valid date from user input"""
    default = datetime.today().strftime(date_format) if allow_default else None
    return validate_input(
        prompt,
        validate_date,
        "Invalid date format. Please enter the date in dd-mm-yyyy format",
        allow_empty=allow_default,
        default_value=default
    )


def get_amount() -> float:
    """Get a valid amount from user input"""
    return validate_input(
        "Enter the amount: ",
        validate_amount,
        "Invalid amount",
    )


def get_category() -> str:
    """Get a valid category from user input"""
    return validate_input(
        "Enter the category ('I' for Income or 'E' for Expense): ",
        validate_category,
        "Invalid category",
    )


def get_description() -> str:
    """Get a description from user input"""
    return input("Enter a description (optional): ")
