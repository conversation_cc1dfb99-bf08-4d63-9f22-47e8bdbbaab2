import pandas as pd
import csv
from datetime import datetime
import matplotlib.pyplot as plt
from data_entry import get_amount, get_category, get_date, get_description


class FinanceManager:
    def __init__(self, csv_file="finance_data.csv", date_format="%d-%m-%Y"):
        self.csv_file = csv_file
        self.columns = ["date", "amount", "category", "description"]
        self.date_format = date_format
        self.initialize_csv()
    
    def initialize_csv(self):
        try:
            pd.read_csv(self.csv_file)
        except FileNotFoundError:
            df = pd.DataFrame(columns=self.columns)
            df.to_csv(self.csv_file, index=False)
    
    def add_entry(self, date, amount, category, description):
        new_entry = {
            "date": date,
            "amount": amount,
            "category": category,
            "description": description,
        }
        with open(self.csv_file, "a", newline="") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.columns)
            writer.writerow(new_entry)
        return True
    
    def get_transactions(self, start_date, end_date):
        df = pd.read_csv(self.csv_file)
        df["date"] = pd.to_datetime(df["date"], format=self.date_format)
        start_date = datetime.strptime(start_date, self.date_format)
        end_date = datetime.strptime(end_date, self.date_format)

        mask = (df["date"] >= start_date) & (df["date"] <= end_date)
        return df.loc[mask]
    
    def get_summary(self, df):
        if df.empty:
            return {"income": 0, "expense": 0, "savings": 0}
        
        total_income = df[df["category"] == "Income"]["amount"].sum()
        total_expense = df[df["category"] == "Expense"]["amount"].sum()
        net_savings = total_income - total_expense
        
        return {
            "income": total_income,
            "expense": total_expense,
            "savings": net_savings
        }
    
    def plot_transactions(self, df):
        if df.empty:
            print("No data to plot.")
            return False
            
        df = df.copy()  # Create a copy to avoid modifying the original
        df["date"] = pd.to_datetime(df["date"], format=self.date_format)
        df.set_index("date", inplace=True)

        income_df = df[df["category"] == "Income"].resample("D").sum().fillna(0)
        expense_df = df[df["category"] == "Expense"].resample("D").sum().fillna(0)

        plt.figure(figsize=(10, 5))
        plt.plot(income_df.index, income_df["amount"], label="Income", color="g")
        plt.plot(expense_df.index, expense_df["amount"], label="Expense", color="r")
        plt.xlabel("Date")
        plt.ylabel("Amount")
        plt.title("Income and Expenses Over Time")
        plt.legend()
        plt.grid(True)
        plt.show()
        return True


def main():
    finance = FinanceManager()
    
    while True:
        print("\n===== CashSquad Finance Tracker =====")
        print("1. Add a new transaction")
        print("2. View transactions and summary within a date range")
        print("3. Exit")
        choice = input("Enter your choice (1-3): ")

        if choice == "1":
            date = get_date(
                "Enter the date (dd-mm-yyyy) or press Enter for today: ",
                allow_default=True,
            )
            amount = get_amount()
            category = get_category()
            description = get_description()
            
            if finance.add_entry(date, amount, category, description):
                print("Entry added successfully")
                
        elif choice == "2":
            start_date = get_date("Enter the start date (dd-mm-yyyy): ")
            end_date = get_date("Enter the end date (dd-mm-yyyy): ")
            
            df = finance.get_transactions(start_date, end_date)
            
            if df.empty:
                print("No transactions found in the given date range.")
            else:
                print(f"\nTransactions from {start_date} to {end_date}")
                print(df.to_string(
                    index=False, 
                    formatters={"date": lambda x: x.strftime(finance.date_format)}
                ))
                
                summary = finance.get_summary(df)
                print("\nSummary:")
                print(f"Total Income: ${summary['income']:.2f}")
                print(f"Total Expense: ${summary['expense']:.2f}")
                print(f"Net Savings: ${summary['savings']:.2f}")
                
                if input("Do you want to see a plot? (y/n) ").lower() == "y":
                    finance.plot_transactions(df)
                    
        elif choice == "3":
            print("Thank you for using CashSquad Finance Tracker!")
            break
            
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")


if __name__ == "__main__":
    main()
