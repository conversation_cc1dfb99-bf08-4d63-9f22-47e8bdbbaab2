"""Module for generating reports and visualizations"""
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import calendar
from typing import Dict, Any, Optional, List, Tuple

import config


def format_currency(amount: float) -> str:
    """Format amount as currency"""
    return f"{config.CURRENCY_SYMBOL}{amount:.2f}"


def generate_summary(df: pd.DataFrame) -> Dict[str, float]:
    """Generate summary statistics from transaction data"""
    if df.empty:
        return {"income": 0, "expense": 0, "savings": 0}
    
    total_income = df[df["category"] == "Income"]["amount"].sum()
    total_expense = df[df["category"] == "Expense"]["amount"].sum()
    net_savings = total_income - total_expense
    
    return {
        "income": total_income,
        "expense": total_expense,
        "savings": net_savings
    }


def plot_transactions_over_time(df: pd.DataFrame, date_format: str) -> bool:
    """Plot income and expenses over time"""
    if df.empty:
        print("No data to plot.")
        return False
        
    df = df.copy()  # Create a copy to avoid modifying the original
    df["date"] = pd.to_datetime(df["date"], format=date_format)
    df.set_index("date", inplace=True)

    income_df = df[df["category"] == "Income"].resample("D").sum().fillna(0)
    expense_df = df[df["category"] == "Expense"].resample("D").sum().fillna(0)

    plt.figure(figsize=config.PLOT_FIGSIZE)
    plt.plot(income_df.index, income_df["amount"], label="Income", color=config.INCOME_COLOR)
    plt.plot(expense_df.index, expense_df["amount"], label="Expense", color=config.EXPENSE_COLOR)
    plt.xlabel("Date")
    plt.ylabel(f"Amount ({config.CURRENCY_SYMBOL})")
    plt.title("Income and Expenses Over Time")
    plt.legend()
    plt.grid(True)
    plt.show()
    return True


def plot_monthly_summary(df: pd.DataFrame, date_format: str) -> bool:
    """Plot monthly income and expenses as a bar chart"""
    if df.empty:
        print("No data to plot.")
        return False
        
    df = df.copy()
    df["date"] = pd.to_datetime(df["date"], format=date_format)
    df["month"] = df["date"].dt.strftime("%Y-%m")
    
    monthly = df.groupby(["month", "category"])["amount"].sum().unstack().fillna(0)
    
    if "Income" not in monthly.columns:
        monthly["Income"] = 0
    if "Expense" not in monthly.columns:
        monthly["Expense"] = 0
    
    monthly["Savings"] = monthly["Income"] - monthly["Expense"]
    
    monthly.plot(kind="bar", figsize=config.PLOT_FIGSIZE)
    plt.xlabel("Month")
    plt.ylabel(f"Amount ({config.CURRENCY_SYMBOL})")
    plt.title("Monthly Financial Summary")
    plt.grid(True, axis="y")
    plt.show()
    return True